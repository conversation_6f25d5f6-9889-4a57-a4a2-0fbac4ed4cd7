import pyautogui
import time
import numpy as np
from mss import mss  # Much faster screen capture than PIL
import cv2  # For faster image processing
import keyboard  # For even faster key presses than pyautogui

def is_target_color(r, g, b, threshold=60):
    """
    Check if a pixel with RGB values is a target color (red or brown/bronze).

    Args:
        r (int): Red component
        g (int): Green component
        b (int): Blue component
        threshold (int): Threshold for color intensity

    Returns:
        bool: True if the pixel is a target color, False otherwise
    """
    # Check for red color
    is_red = r > threshold and r > (g * 1.5) and r > (b * 1.5)

    # Check for brown/bronze color
    # Brown/bronze typically has red and green components higher than blue
    is_brown = (r > threshold and g > threshold * 0.5 and
               r > b * 1.5 and g > b * 1.2 and
               r > g * 1.1)  # Red slightly higher than green

    return is_red or is_brown

def check_all_pixels(pixels_to_check, sct, threshold=60):
    """
    Check all specified pixels at once and return their detection status.

    Args:
        pixels_to_check (list): List of dictionaries with pixel coordinates, type (offset/trigger), and id.
        sct (mss.mss): MSS screen capture object.
        threshold (int): Threshold for color intensity.

    Returns:
        dict: A dictionary where keys are f"{id}_{type}" (e.g., "object_w_offset") and values are booleans
              indicating if the target color was detected at that pixel.
    """
    if not pixels_to_check:
        return {}

    # For maximum performance, create a single region that encompasses all pixels
    min_x = min(pixel["x"] for pixel in pixels_to_check)
    min_y = min(pixel["y"] for pixel in pixels_to_check)
    max_x = max(pixel["x"] for pixel in pixels_to_check)
    max_y = max(pixel["y"] for pixel in pixels_to_check)

    # Create a region that includes all pixels
    region = {
        "top": min_y,
        "left": min_x,
        "width": max_x - min_x + 1,
        "height": max_y - min_y + 1
    }

    detected_status = {}

    try:
        # Capture the region containing all pixels at once
        img = np.array(sct.grab(region))

        # Check each pixel in the captured region
        for pixel in pixels_to_check:
            # Calculate relative coordinates in the captured image
            rel_x = pixel["x"] - min_x
            rel_y = pixel["y"] - min_y

            # Ensure coordinates are within the captured image bounds
            if 0 <= rel_y < img.shape[0] and 0 <= rel_x < img.shape[1]:
                # Get the BGR values (OpenCV format)
                b, g, r = img[rel_y, rel_x, :3]

                # Check if this is a target color
                is_detected = is_target_color(r, g, b, threshold)
                detected_status[f"{pixel['id']}_{pixel['type']}"] = is_detected
            else:
                # If pixel is out of bounds, consider it not detected
                detected_status[f"{pixel['id']}_{pixel['type']}"] = False
    except Exception as e:
        # Log the exception for debugging, but don't stop the script
        # print(f"Error in check_all_pixels: {e}")
        # If an error occurs, assume no pixels are detected for this frame
        for pixel in pixels_to_check:
            detected_status[f"{pixel['id']}_{pixel['type']}"] = False

    return detected_status

def monitor_pixels_and_press_keys():
    """
    Monitor specific pixels on the screen and press corresponding keys
    when red or brown/bronze pixels are detected. Runs at maximum possible speed.
    """
    # Define tracking objects with their trigger and offset coordinates, initial state, and last pressed time
    tracking_objects = [
        {
            "id": "object_w",
            "key": "w",
            "trigger_x": 960, "trigger_y": 728,
            "offset_x": 960, "offset_y": 628,  # 100 pixels up from trigger
            "state": "idle",  # "idle", "offset_detected", "final_detected"
            "last_pressed_time": 0
        },
        {
            "id": "object_a",
            "key": "a",
            "trigger_x": 770, "trigger_y": 905,
            "offset_x": 670, "offset_y": 905,  # 100 pixels left from trigger
            "state": "idle",
            "last_pressed_time": 0
        },
        {
            "id": "object_d",
            "key": "d",
            "trigger_x": 1150, "trigger_y": 907,
            "offset_x": 1250, "offset_y": 907,  # 100 pixels right from trigger
            "state": "idle",
            "last_pressed_time": 0
        }
    ]

    # Cooldown period to prevent rapid re-presses (in seconds)
    COOLDOWN_PERIOD = 0.5

    print("Pixel monitoring started. Press Ctrl+C to exit.")
    print("Monitoring at maximum speed with threshold 60:")
    for obj in tracking_objects:
        print(f"  Object '{obj['id']}': Trigger X: {obj['trigger_x']}, Y: {obj['trigger_y']} | Offset X: {obj['offset_x']}, Y: {obj['offset_y']} - Press '{obj['key'].upper()}'")

    # Track performance metrics
    frame_count = 0
    start_time_total = time.time()
    last_fps_print_time = time.time()

    # Initialize the screen capture object (much faster than PIL)
    with mss() as sct:
        # Pre-compile key press functions for better performance
        # Use keyboard library for faster key presses
        key_press_funcs = {
            "w": lambda: keyboard.press_and_release("w"),
            "a": lambda: keyboard.press_and_release("a"),
            "d": lambda: keyboard.press_and_release("d")
        }

        # Try to use keyboard module, fallback to pyautogui if it fails
        try:
            # Test keyboard module
            keyboard.press_and_release("shift")
            print("Using keyboard module for faster key presses")
        except Exception:
            # If keyboard module fails, use pyautogui instead
            key_press_funcs = {
                "w": lambda: pyautogui.press("w"),
                "a": lambda: pyautogui.press("a"),
                "d": lambda: pyautogui.press("d")
            }
            # Reduce PyAutoGUI's pause time for faster key presses
            pyautogui.PAUSE = 0.01
            print("Using PyAutoGUI fallback for key presses")

        try:
            while True:
                current_time = time.time()
                # Prepare a list of all unique pixels to monitor for the current frame
                all_pixels_to_check = []
                for obj in tracking_objects:
                    all_pixels_to_check.append({"x": obj["offset_x"], "y": obj["offset_y"], "type": "offset", "id": obj["id"]})
                    all_pixels_to_check.append({"x": obj["trigger_x"], "y": obj["trigger_y"], "type": "trigger", "id": obj["id"]})

                # Check all required pixels at once
                detected_pixels = check_all_pixels(all_pixels_to_check, sct)

                # Process each tracking object based on its state
                for obj in tracking_objects:
                    offset_detected = detected_pixels.get(f"{obj['id']}_offset", False)
                    trigger_detected = detected_pixels.get(f"{obj['id']}_trigger", False)

                    if obj["state"] == "idle":
                        if offset_detected:
                            obj["state"] = "offset_detected"
                            # print(f"Offset detected for {obj['id']}")
                    elif obj["state"] == "offset_detected":
                        if trigger_detected:
                            if (current_time - obj["last_pressed_time"]) > COOLDOWN_PERIOD:
                                key_press_funcs[obj["key"]]()
                                obj["last_pressed_time"] = current_time
                                obj["state"] = "final_detected"
                                print(f"Pressed '{obj['key'].upper()}' for {obj['id']}")
                        elif not offset_detected: # Object moved away from offset
                            obj["state"] = "idle"
                            # print(f"Resetting state for {obj['id']} (lost offset)")
                    elif obj["state"] == "final_detected":
                        if not trigger_detected: # Object moved away from final trigger
                            obj["state"] = "idle"
                            # print(f"Resetting state for {obj['id']} (lost final trigger)")

                # Update performance counter
                frame_count += 1

                # Print FPS every second
                if current_time - last_fps_print_time >= 1.0:
                    elapsed = current_time - start_time_total
                    fps = frame_count / elapsed if elapsed > 0 else 0
                    # print(f"Current performance: {fps:.2f} FPS") # Commented out for cleaner output
                    last_fps_print_time = current_time

                    # Reset counters every 1000 frames to get current performance
                    if frame_count >= 1000:
                        frame_count = 0
                        start_time_total = time.time()

        except KeyboardInterrupt:
            print("\nPixel monitoring stopped.")

if __name__ == "__main__":
    # Add a small delay before starting to give user time to switch windows
    print("Starting in 3 seconds...")
    time.sleep(3)
    monitor_pixels_and_press_keys()
